<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简介 - 备案信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-top: 50px;
            border-radius: 8px;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #007acc;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #007acc;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid #007acc;
        }

        .info-item strong {
            color: #007acc;
            display: block;
            margin-bottom: 5px;
        }

        .contact-info {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #007acc;
        }

        .contact-info p {
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 0.9em;
        }

        .beian-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .beian-info h3 {
            color: #856404;
            margin-bottom: 10px;
        }

        @media (max-width: 600px) {
            .container {
                margin: 20px;
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>个人简介</h1>
            <p>Personal Profile for Website Registration</p>
        </div>

        <div class="section">
            <h2>基本信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <strong>姓名：</strong>
                    [请填写真实姓名]
                </div>
                <div class="info-item">
                    <strong>性别：</strong>
                    [请填写性别]
                </div>
                <div class="info-item">
                    <strong>身份证号：</strong>
                    [请填写身份证号码]
                </div>
                <div class="info-item">
                    <strong>联系电话：</strong>
                    [请填写手机号码]
                </div>
            </div>
        </div>

        <div class="section">
            <h2>联系方式</h2>
            <div class="contact-info">
                <p><strong>邮箱地址：</strong> [请填写邮箱地址]</p>
                <p><strong>通讯地址：</strong> [请填写详细地址]</p>
                <p><strong>邮政编码：</strong> [请填写邮政编码]</p>
            </div>
        </div>

        <div class="section">
            <h2>网站信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <strong>网站名称：</strong>
                    [请填写网站名称]
                </div>
                <div class="info-item">
                    <strong>网站域名：</strong>
                    [请填写域名]
                </div>
                <div class="info-item">
                    <strong>网站性质：</strong>
                    个人网站
                </div>
                <div class="info-item">
                    <strong>网站用途：</strong>
                    [请填写网站用途说明]
                </div>
            </div>
        </div>

        <div class="section">
            <h2>备案承诺</h2>
            <div class="beian-info">
                <h3>备案信息真实性承诺</h3>
                <p>本人承诺所填写的备案信息真实有效，网站内容符合国家相关法律法规要求，不含有违法违规信息。如有虚假信息，愿承担相应法律责任。</p>
                <p><strong>承诺人：</strong> [请填写姓名]</p>
                <p><strong>承诺日期：</strong> [请填写日期]</p>
            </div>
        </div>

        <div class="footer">
            <p>本页面仅用于网站备案信息展示</p>
            <p>© 2024 个人网站备案信息页面</p>
        </div>
    </div>
</body>
</html>