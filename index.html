<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简介</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-top: 50px;
            border-radius: 15px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            margin: -20px -20px 30px -20px;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3em;
            border: 4px solid rgba(255,255,255,0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            margin-bottom: 30px;
        }

        .section h2 {
            color: #667eea;
            border-left: 4px solid #667eea;
            padding-left: 15px;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 500;
        }

        .about-text {
            background-color: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            font-size: 1.1em;
            line-height: 1.8;
            border-left: 4px solid #667eea;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .skill-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
            transition: transform 0.3s ease;
        }

        .skill-item:hover {
            transform: translateY(-5px);
        }

        .contact-info {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .contact-item i {
            font-size: 1.2em;
            width: 20px;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .project-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .project-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }

        @media (max-width: 600px) {
            .container {
                margin: 20px;
                padding: 15px;
            }

            .header {
                margin: -15px -15px 30px -15px;
                padding: 30px 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .skills-grid,
            .projects-grid {
                grid-template-columns: 1fr;
            }

            .contact-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="avatar">👨‍💻</div>
            <h1>张三</h1>
            <p>全栈开发工程师 | 技术爱好者</p>
        </div>

        <div class="section">
            <h2>关于我</h2>
            <div class="about-text">
                <p>你好！我是一名充满热情的全栈开发工程师，拥有5年的软件开发经验。我热爱编程，喜欢探索新技术，致力于创建优雅、高效的解决方案。</p>
                <p>我相信技术的力量可以改变世界，通过代码可以创造出令人惊叹的产品。在工作之余，我喜欢阅读技术博客、参与开源项目，并与技术社区分享我的经验和见解。</p>
            </div>
        </div>

        <div class="section">
            <h2>技能专长</h2>
            <div class="skills-grid">
                <div class="skill-item">JavaScript / TypeScript</div>
                <div class="skill-item">React / Vue.js</div>
                <div class="skill-item">Node.js / Express</div>
                <div class="skill-item">Python / Django</div>
                <div class="skill-item">MySQL / MongoDB</div>
                <div class="skill-item">Docker / Kubernetes</div>
                <div class="skill-item">AWS / 阿里云</div>
                <div class="skill-item">Git / CI/CD</div>
            </div>
        </div>

        <div class="section">
            <h2>项目经验</h2>
            <div class="projects-grid">
                <div class="project-card">
                    <h3>电商平台系统</h3>
                    <p>使用React + Node.js构建的全栈电商平台，支持用户注册、商品浏览、购物车、订单管理等功能。采用微服务架构，支持高并发访问。</p>
                </div>
                <div class="project-card">
                    <h3>数据可视化平台</h3>
                    <p>基于Vue.js和D3.js开发的数据可视化平台，帮助企业实时监控业务数据，提供多种图表类型和交互功能。</p>
                </div>
                <div class="project-card">
                    <h3>移动端APP</h3>
                    <p>使用React Native开发的跨平台移动应用，集成了地图服务、推送通知、支付功能等，用户体验流畅。</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>联系方式</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <i>📧</i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i>📱</i>
                    <span>+86 138-0000-0000</span>
                </div>
                <div class="contact-item">
                    <i>🌐</i>
                    <span>github.com/zhangsan</span>
                </div>
                <div class="contact-item">
                    <i>📍</i>
                    <span>北京市朝阳区</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>感谢您的关注！期待与您的合作交流</p>
            <p>© 2024 张三的个人简介</p>
        </div>
    </div>
</body>
</html>